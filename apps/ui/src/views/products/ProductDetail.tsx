import { useContext, useEffect, useState, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import isEqual from "lodash/isEqual";
import { LocationContext, ProductContext } from "../../contexts";
import PageContent from "../../ui/PageContent";
import { InfoTable } from "../../ui/InfoTable";
import { useTranslation } from "react-i18next";
import api from "../../api";
import Button from "../../ui/Button";
import Card from "../../ui/Card";
import Modal from "../../ui/Modal";
import {
  FiEdit,
  FiTrash2,
  FiChevronDown,
  FiChevronUp,
  FiArrowLeft,
} from "react-icons/fi";
import { Product, ProductParams } from "../../types";
import TextInput from "../../ui/form/TextInput";
import { CategorySelect } from "../../ui/form/CategorySelect";
import CheckboxInput from "../../ui/form/CheckboxInput";
import ImageUploadWithDragDrop from "../../components/ImageUploadWithDragDrop";
import { SparklesIcon, PlusIcon } from "../../ui/icons";
import AIStatusBadge from "../../components/AIStatusBadge";
import MarkdownRenderer from "../../ui/MarkdownRenderer";
import MarkdownEditor from "../../ui/MarkdownEditor";
import "./ProductDetail.css";

// Extend Product interface to include enhancement fields
interface EnhancedProduct extends Product {
  enhancement_status?: string;
  enhancement_error?: string;
}

// Badge component for AI enhanced fields
const AIBadge = () => (
  <span className="ai-badge">
    <SparklesIcon className="w-3 h-3 mr-1" />
    <span>AI Enhanced</span>
  </span>
);

// Enhancement status badge colors
const getEnhancementStatusColor = (status?: string) => {
  switch (status) {
    case "complete":
      return "green";
    case "failed":
      return "red";
    case "skipped":
      return "orange";
    case "pending":
      return "blue";
    default:
      return "gray";
  }
};

// Enhancement status badge labels
const getEnhancementStatusLabel = (status?: string) => {
  switch (status) {
    case "complete":
      return "AI Enhanced";
    case "failed":
      return "Enhancement Failed";
    case "skipped":
      return "Enhancement Skipped";
    case "pending":
      return "Enhancement Pending";
    default:
      return "Not Enhanced";
  }
};

export default function ProductDetail() {
  const { t } = useTranslation();
  const { entityId: productId } = useParams();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);
  const [product, setProduct] = useContext<
    [Product | null, (p: Product | null) => void]
  >(ProductContext as any);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEnhanceModalOpen, setIsEnhanceModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<ProductParams | null>(null);
  const [originalProductData, setOriginalProductData] =
    useState<Product | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [showAdditionalDetails, setShowAdditionalDetails] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [showAdditionalDetailsEdit, setShowAdditionalDetailsEdit] =
    useState(false);

  // Featured products state
  const [featuredStatus, setFeaturedStatus] = useState<{
    isFeatured: boolean;
    isTopPick: boolean;
    featuredId?: number;
    isUpdating: boolean;
  }>({
    isFeatured: false,
    isTopPick: false,
    isUpdating: false,
  });

  // Set featured status from product data
  const updateFeaturedStatusFromProduct = useCallback(() => {
    if (!product) return;

    // Use the featured status from the product data itself
    setFeaturedStatus({
      isFeatured: !!(product as any).is_featured,
      isTopPick: !!(product as any).is_top_pick,
      featuredId: (product as any).featured_id,
      isUpdating: false,
    });
  }, [product]);

  // Toggle featured status
  const toggleFeatured = async (checked: boolean) => {
    if (!location || !product) return;

    setFeaturedStatus((prev) => ({ ...prev, isUpdating: true }));

    try {
      if (checked) {
        // Add as featured
        const response = await api.featuredProducts.addFeatured(location.id, {
          product_id: product.id,
          is_top_pick: featuredStatus.isTopPick,
          active: true,
        });
        setFeaturedStatus((prev) => ({
          ...prev,
          isFeatured: true,
          featuredId: response.id,
          isUpdating: false,
        }));
      } else {
        // Remove from featured
        if (featuredStatus.featuredId) {
          await api.featuredProducts.removeFeatured(
            location.id,
            featuredStatus.featuredId
          );
        }
        setFeaturedStatus({
          isFeatured: false,
          isTopPick: false,
          featuredId: undefined,
          isUpdating: false,
        });
      }
    } catch (error) {
      console.error("Error toggling featured status:", error);
      setFeaturedStatus((prev) => ({ ...prev, isUpdating: false }));
    }
  };

  // Toggle top pick status
  const toggleTopPick = async (checked: boolean) => {
    if (
      !location ||
      !product ||
      !featuredStatus.isFeatured ||
      !featuredStatus.featuredId
    )
      return;

    setFeaturedStatus((prev) => ({ ...prev, isUpdating: true }));

    try {
      await api.featuredProducts.updateFeatured(
        location.id,
        featuredStatus.featuredId,
        {
          is_top_pick: checked,
        }
      );
      setFeaturedStatus((prev) => ({
        ...prev,
        isTopPick: checked,
        isUpdating: false,
      }));
    } catch (error) {
      console.error("Error toggling top pick status:", error);
      setFeaturedStatus((prev) => ({ ...prev, isUpdating: false }));
    }
  };

  useEffect(() => {
    if (location && productId) {
      api.products
        .get(location.id, parseInt(productId))
        .then((fetchedProduct: Product) => {
          setProduct(fetchedProduct);
          setOriginalProductData(fetchedProduct);
        });
    }
  }, [location, productId, setProduct]);

  // Update featured status when product changes
  useEffect(() => {
    updateFeaturedStatusFromProduct();
  }, [product, updateFeaturedStatusFromProduct]);

  useEffect(() => {
    if (originalProductData) {
      setEditFormData({
        ...(originalProductData as any),
        brand_name: originalProductData.brand_name || "",
        product_description: originalProductData.product_description || "",
        short_description: originalProductData.short_description || "",
        effects: originalProductData.effects || null,
        mood: originalProductData.mood || [],
        product_tags: originalProductData.product_tags || [],
        image_url: originalProductData.image_url || "",
        category: originalProductData.category || "",
        subcategory: originalProductData.subcategory || "",
        display_weight: originalProductData.display_weight || "",
        quantity_per_package:
          originalProductData.quantity_per_package === undefined
            ? null
            : originalProductData.quantity_per_package,
        percentage_thc:
          originalProductData.percentage_thc === undefined
            ? null
            : originalProductData.percentage_thc,
        percentage_cbd:
          originalProductData.percentage_cbd === undefined
            ? null
            : originalProductData.percentage_cbd,
        mg_thc:
          originalProductData.mg_thc === undefined
            ? null
            : originalProductData.mg_thc,
        mg_cbd:
          originalProductData.mg_cbd === undefined
            ? null
            : originalProductData.mg_cbd,
        latest_price:
          originalProductData.latest_price === undefined
            ? null
            : originalProductData.latest_price,
        menu_provider: originalProductData.menu_provider || "",
        url: originalProductData.url || "",
      });

      if (originalProductData.image_url) {
        setImagePreview(originalProductData.image_url);
      } else {
        setImagePreview(null);
      }
    } else {
      setEditFormData(null);
      setImagePreview(null);
    }
  }, [originalProductData]);

  // Function to check if a field was AI enhanced
  const isFieldEnhanced = (fieldName: keyof Product) => {
    return product?.ai_enhanced_fields?.includes(fieldName);
  };

  // Function to enhance a single product
  const handleEnhanceProduct = async () => {
    console.log("enhanceProducts", location.id, JSON.stringify(product));
    if (!location || !product?.meta_sku || !productId) return;

    setIsEnhancing(true);
    try {
      // Call the enhance endpoint with this specific product's meta_sku
      // Use async mode for cost-efficient processing
      const result = await api.products.enhance(location.id, {
        meta_sku: product?.meta_sku,
        async: true,
      });

      // Check if this is async processing
      if (result.async) {
        // Show a different message for async processing
        alert(
          "Product enhancement has been queued and will be processed in the background. " +
            "This may take a few minutes to complete. You can continue using the application."
        );
        setIsEnhanceModalOpen(false);
      } else {
        // Reload the product data to get the enhancements for synchronous processing
        const updatedProduct = await api.products.get(location.id, productId);
        setProduct(updatedProduct);
        setIsEnhanceModalOpen(false);

        // Show success message
        alert(
          `Product enhanced successfully! ${result.enhanced_count} fields were updated.`
        );
      }
    } catch (error) {
      console.error("Error enhancing product:", error);
      alert("Failed to enhance product. Please try again.");
    } finally {
      setIsEnhancing(false);
    }
  };

  // Move hooks before early return to maintain consistent hook order
  const handleInputChange = useCallback(
    (field: keyof ProductParams, value: any) => {
      setEditFormData((prevFormData) => {
        if (!prevFormData) return prevFormData;

        let processedValue = value;
        // Special handling for array fields (mood, product_tags)
        if (field === "mood" || field === "product_tags") {
          processedValue =
            typeof value === "string"
              ? value
                  .split(",")
                  .map((item) => item.trim())
                  .filter((item) => item !== "")
              : Array.isArray(value)
              ? value
              : [];
        }
        // Special handling for effects (object)
        else if (field === "effects") {
          if (typeof value === "string") {
            try {
              // Allow empty string to clear the object
              processedValue = value.trim() === "" ? null : JSON.parse(value);
            } catch (e) {
              // If JSON is invalid, keep the raw string temporarily
              // or indicate an error. For now, keep the string.
              // A better approach might involve a dedicated JSON editor or validation feedback.
              console.warn("Invalid JSON format for effects:", value);
              processedValue = value; // Keep the string to allow user to correct it
            }
          } else {
            // If it's already an object or null, keep it as is
            processedValue = typeof value === "object" ? value : null;
          }
        }

        return {
          ...prevFormData,
          [field]: processedValue,
        };
      });
    },
    []
  );

  if (!product || !editFormData) return null;

  // Add image handling functions
  const handleImageChange = (file: File | null) => {
    if (file) {
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
        // Update form data with new image (will be uploaded when form is submitted)
        if (editFormData) {
          // Use type assertion here to handle the new_image_file property
          setEditFormData({
            ...editFormData,
            new_image_file: file,
          } as ProductParams & { new_image_file: File });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const clearImage = () => {
    setImagePreview(null);
    if (editFormData) {
      // Create a new object without the new_image_file property
      const { new_image_file, ...rest } = editFormData as ProductParams & {
        new_image_file?: File;
      };
      setEditFormData(rest);
    }
  };

  const handleSubmit = async () => {
    if (!location || !productId || !editFormData || !originalProductData)
      return;

    setIsSubmitting(true);

    // --- Diffing Logic ---
    const diffData: Partial<ProductParams> = {};
    const keys = Object.keys(editFormData) as (keyof ProductParams)[];

    for (const key of keys) {
      const currentValue = editFormData[key];
      const originalValue = (originalProductData as any)[key];

      // Use lodash isEqual for deep comparison of objects/arrays
      // Treat null and undefined as potentially different from each other or other values
      if (!isEqual(currentValue, originalValue)) {
        (diffData as any)[key] = currentValue; // Add changed value to diff
      }
    }

    // Special handling for image file upload (not part of diff data)
    let imageUploadFile: File | undefined = undefined;
    if ((editFormData as any).new_image_file) {
      imageUploadFile = (editFormData as any).new_image_file;
      // Remove new_image_file from diffData if it exists, it's handled separately
      delete (diffData as any).new_image_file;
    }

    // If image_url itself changed (and no file uploaded), include it in diff
    // The previous check handles the case where !isEqual correctly

    // --- End Diffing Logic ---

    // Check if there's anything to submit (diff or image upload)
    if (Object.keys(diffData).length === 0 && !imageUploadFile) {
      console.log("No changes detected.");
      setIsEditModalOpen(false);
      setIsSubmitting(false);
      return; // Nothing to update
    }

    try {
      const finalDiffData = { ...diffData }; // Copy diff data

      // Ensure empty strings/arrays are converted to null for nullable fields IN THE DIFF
      const fieldsToNullCheck: (keyof ProductParams)[] = [
        "effects",
        "mood",
        "product_tags",
      ];
      for (const field of fieldsToNullCheck) {
        if (Object.prototype.hasOwnProperty.call(finalDiffData, field)) {
          const value = finalDiffData[field];
          if (value === "" || (Array.isArray(value) && value.length === 0)) {
            (finalDiffData as any)[field] = null;
          } else if (
            field === "effects" &&
            typeof value === "object" &&
            value !== null &&
            Object.keys(value).length === 0
          ) {
            (finalDiffData as any)[field] = null; // Nullify empty effects object
          }
        }
      }

      // Handle image changes
      let updatedProduct;

      if (imageUploadFile) {
        // Use multipart form data with file upload
        const formDataToSend = new FormData();

        // Add all diff data fields
        Object.entries(finalDiffData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
              formDataToSend.append(key, JSON.stringify(value));
            } else {
              formDataToSend.append(key, String(value));
            }
          }
        });

        // Add the image file
        formDataToSend.append("productImage", imageUploadFile);

        // Update product with image in one request
        const response = await fetch(
          `/api/admin/locations/${location.id}/products/${productId}`,
          {
            method: "PATCH",
            body: formDataToSend,
            headers: {
              Accept: "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `Request failed: ${response.statusText}`
          );
        }

        updatedProduct = await response.json();
      } else if (
        finalDiffData.image_url &&
        originalProductData &&
        finalDiffData.image_url !== originalProductData.image_url
      ) {
        // Handle image URL change with download
        const productDataWithDownload = {
          ...finalDiffData,
          image_url: finalDiffData.image_url,
        };

        // Update product with image download in one request
        updatedProduct = await api.products.update(
          location.id,
          parseInt(productId),
          productDataWithDownload as ProductParams
        );
      } else {
        // Check if diff still has data after processing nulls
        if (Object.keys(finalDiffData).length === 0) {
          console.log("No effective changes to submit after processing.");
          setIsEditModalOpen(false);
          setIsSubmitting(false);
          return;
        }

        // Regular update without image changes
        updatedProduct = await api.products.update(
          location.id,
          parseInt(productId),
          finalDiffData as ProductParams
        );
      }

      setProduct(updatedProduct); // Update display context
      setOriginalProductData(updatedProduct); // Update original data reference
      setIsEditModalOpen(false);
    } catch (error) {
      console.error("Error updating product:", error);
      alert("Failed to update product. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!location || !productId) return;

    setIsSubmitting(true);
    try {
      await api.products.delete(location.id, parseInt(productId));
      navigate(`/locations/${location.id}/products`);
    } catch (error) {
      console.error("Error deleting product:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenEditModal = () => {
    setIsEditModalOpen(true);
    // Reset image preview to current product image
    if (product?.image_url) {
      setImagePreview(product.image_url);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleOpenDeleteModal = () => {
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  const handleOpenEnhanceModal = () => {
    setIsEnhanceModalOpen(true);
  };

  const handleCloseEnhanceModal = () => {
    setIsEnhanceModalOpen(false);
  };

  const toggleAdditionalDetails = () => {
    setShowAdditionalDetails(!showAdditionalDetails);
  };

  const toggleAdditionalDetailsEdit = () => {
    setShowAdditionalDetailsEdit(!showAdditionalDetailsEdit);
  };

  const goBackToProducts = () => {
    navigate(`/locations/${location.id}/products`);
  };

  const goToAddProduct = () => {
    navigate(`/locations/${location.id}/products/add`);
  };

  // Placeholder image if none is provided
  const fallbackImage = "https://via.placeholder.com/400x400?text=No+Image";

  // Actions for modals
  const editModalActions = (
    <div className="modal-actions">
      <Button variant="secondary" onClick={handleCloseEditModal}>
        {t("cancel")}
      </Button>
      <Button onClick={handleSubmit} disabled={isSubmitting}>
        {isSubmitting ? t("saving") : t("save")}
      </Button>
    </div>
  );

  const deleteModalActions = (
    <div className="modal-actions">
      <Button variant="secondary" onClick={handleCloseDeleteModal}>
        {t("cancel")}
      </Button>
      <Button
        variant="destructive"
        onClick={handleDelete}
        disabled={isSubmitting}
      >
        {t("delete")}
      </Button>
    </div>
  );

  const enhanceModalActions = (
    <div className="modal-actions">
      <Button variant="secondary" onClick={handleCloseEnhanceModal}>
        {t("cancel")}
      </Button>
      <Button
        onClick={handleEnhanceProduct}
        disabled={isEnhancing}
        icon={<SparklesIcon />}
      >
        {isEnhancing ? "Enhancing..." : "Enhance Product"}
      </Button>
    </div>
  );

  if (isSubmitting) {
    return <div>Loading...</div>;
  }

  if (!product) {
    return <div>Product not found</div>;
  }

  // Format values for effects and mood that could be arrays or null
  const formatArrayValue = (value: string | string[] | undefined | null) => {
    if (!value) return "";
    if (typeof value === "string") return value;
    return value.join(", ");
  };

  // Format effects for better display
  const formatEffectsValue = (effects: any) => {
    if (!effects) return "-";

    if (typeof effects === "string") {
      return effects;
    }

    if (Array.isArray(effects)) {
      return effects.join(", ");
    }

    if (typeof effects === "object") {
      // Handle structured effects object
      const parts = [];

      if (effects.description) {
        parts.push(effects.description);
      }

      if (effects.onset) {
        parts.push(`Onset: ${effects.onset}`);
      }

      if (effects.duration) {
        parts.push(`Duration: ${effects.duration}`);
      }

      // If it has other properties, show them as key-value pairs
      const otherProps = Object.entries(effects)
        .filter(([key]) => !["description", "onset", "duration"].includes(key))
        .map(([key, value]) => `${key}: ${value}`);

      parts.push(...otherProps);

      return parts.length > 0
        ? parts.join(" • ")
        : JSON.stringify(effects, null, 2);
    }

    return String(effects);
  };

  // Safe JSON stringify to handle circular references
  const getProductJson = () => {
    try {
      return JSON.stringify(product, null, 2);
    } catch (e) {
      return "Error displaying product data";
    }
  };

  return (
    <PageContent
      title={
        <div className="flex items-center">
          <button
            onClick={goBackToProducts}
            className="mr-4 text-gray-600 hover:text-gray-900"
          >
            <FiArrowLeft size={20} />
          </button>
          <span>{product.product_name}</span>
        </div>
      }
      actions={
        <div className="action-group">
          <Button
            icon={<PlusIcon />}
            onClick={goToAddProduct}
          >
            {t("add_product")}
          </Button>
          <Button
            variant="secondary"
            onClick={handleOpenEnhanceModal}
            icon={<SparklesIcon />}
          >
            Enhance with AI
          </Button>
          <Button
            variant="secondary"
            onClick={handleOpenEditModal}
            icon={<FiEdit />}
          >
            {t("edit")}
          </Button>
          <Button
            variant="destructive"
            onClick={handleOpenDeleteModal}
            icon={<FiTrash2 />}
          >
            {t("delete")}
          </Button>
        </div>
      }
    >
      <div className="product-detail-grid">
        <div className="product-image-section">
          <div className="product-image-container">
            <img
              src={product.image_url || fallbackImage}
              alt={product.product_name}
              className="product-image"
              onError={(e) => {
                (e.target as HTMLImageElement).src = fallbackImage;
              }}
            />
          </div>
        </div>

        <div className="product-info-section">
          {/* AI Status Card */}
          <Card className="mb-4">
            <div className="p-4">
              <h3 className="text-lg font-medium mb-3">AI Budtender Status</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <AIStatusBadge
                      product={product}
                      size="large"
                      showActions={true}
                      onEnhanceClick={handleOpenEnhanceModal}
                      onEditClick={handleOpenEditModal}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Featured Products Card */}
          <Card className="mb-4">
            <div className="p-4">
              <h3 className="text-lg font-medium mb-3">Featured Products</h3>
              <div className="space-y-3">
                <CheckboxInput
                  label="Mark as Featured Product"
                  checked={featuredStatus.isFeatured}
                  onChange={toggleFeatured}
                  disabled={featuredStatus.isUpdating}
                />
                {featuredStatus.isFeatured && (
                  <CheckboxInput
                    label="Mark as Top Pick"
                    checked={featuredStatus.isTopPick}
                    onChange={toggleTopPick}
                    disabled={featuredStatus.isUpdating}
                  />
                )}
                {featuredStatus.isUpdating && (
                  <div className="text-sm text-gray-500">Updating...</div>
                )}
              </div>
            </div>
          </Card>

          <Card className="mb-4">
            <div className="p-4">
              <h3 className="text-lg font-medium mb-3">
                {t("product_details")}
              </h3>
              <InfoTable
                rows={{
                  [t("product_name")]: (
                    <div className="flex items-center">
                      {product.product_name}
                      {isFieldEnhanced("product_name") && <AIBadge />}
                    </div>
                  ),
                  [t("brand")]: (
                    <div className="flex items-center">
                      {product.brand_name || "-"}
                      {isFieldEnhanced("brand_name") && <AIBadge />}
                    </div>
                  ),
                  [t("category")]: (
                    <div className="flex items-center">
                      {product.category || "-"}
                      {isFieldEnhanced("category") && <AIBadge />}
                    </div>
                  ),
                  [t("subcategory")]: (
                    <div className="flex items-center">
                      {product.subcategory || "-"}
                      {isFieldEnhanced("subcategory") && <AIBadge />}
                    </div>
                  ),
                  [t("price")]:
                    product.latest_price !== null &&
                    product.latest_price !== undefined
                      ? `$${Number(product.latest_price).toFixed(2)}`
                      : "-",
                  [t("weight")]: product.display_weight || "-",
                  [t("tags")]: (
                    <div className="flex items-center">
                      {product.product_tags?.join(", ") || "-"}
                      {isFieldEnhanced("product_tags") && <AIBadge />}
                    </div>
                  ),
                  [t("THC")]: (
                    <div className="flex items-center">
                      {product.percentage_thc !== null &&
                      product.percentage_thc !== undefined
                        ? `${product.percentage_thc}%`
                        : "-"}
                      {isFieldEnhanced("percentage_thc") && <AIBadge />}
                    </div>
                  ),
                  [t("CBD")]: (
                    <div className="flex items-center">
                      {product.percentage_cbd !== null &&
                      product.percentage_cbd !== undefined
                        ? `${product.percentage_cbd}%`
                        : "-"}
                      {isFieldEnhanced("percentage_cbd") && <AIBadge />}
                    </div>
                  ),
                  [t("product_description")]: (
                    <div className="flex items-start">
                      {product.product_description ? (
                        <div className="flex-1">
                          <MarkdownRenderer
                            content={product.product_description}
                            compact
                            autoFormat={true}
                          />
                        </div>
                      ) : (
                        "-"
                      )}
                      {isFieldEnhanced("product_description") && (
                        <div className="ml-2 flex-shrink-0">
                          <AIBadge />
                        </div>
                      )}
                    </div>
                  ),
                  [t("short_description")]: (
                    <div className="flex items-start">
                      {product.short_description ? (
                        <div className="flex-1">
                          <MarkdownRenderer
                            content={product.short_description}
                            compact
                            autoFormat={true}
                          />
                        </div>
                      ) : (
                        "-"
                      )}
                      {isFieldEnhanced("short_description") && (
                        <div className="ml-2 flex-shrink-0">
                          <AIBadge />
                        </div>
                      )}
                    </div>
                  ),
                  [t("mood")]: (
                    <div className="flex items-center">
                      {formatArrayValue(product.mood) || "-"}
                      {isFieldEnhanced("mood") && <AIBadge />}
                    </div>
                  ),
                  [t("effects")]: (
                    <div className="flex items-center">
                      {formatEffectsValue(product.effects) || "-"}
                      {isFieldEnhanced("effects") && <AIBadge />}
                    </div>
                  ),
                }}
              />
            </div>
          </Card>

          <Card className="mb-4">
            <div className="p-4">
              <div
                className="flex items-center cursor-pointer"
                onClick={toggleAdditionalDetails}
              >
                <span className="text-lg font-medium mr-2">
                  {t("additional_details")}
                </span>
                {showAdditionalDetails ? <FiChevronUp /> : <FiChevronDown />}
              </div>

              {showAdditionalDetails && (
                <InfoTable
                  rows={{
                    [t("sku")]: product.meta_sku,
                    [t("retailer_id")]: product.retailer_id,
                    [t("medical")]: product.medical ? "Yes" : "No",
                    [t("recreational")]: product.recreational ? "Yes" : "No",
                    [t("menu_provider")]: product.menu_provider || "-",
                    [t("quantity_per_package")]:
                      product.quantity_per_package?.toString() || "-",
                    [t("raw_product_name")]: product.raw_product_name,
                    [t("raw_product_category")]:
                      product.raw_product_category || "-",
                    [t("raw_subcategory")]: product.raw_subcategory || "-",
                    [t("raw_weight_string")]: product.raw_weight_string || "-",
                    [t("url")]: product.url ? (
                      <a
                        href={product.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {product.url}
                      </a>
                    ) : (
                      "-"
                    ),
                  }}
                />
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Edit Modal */}
      <Modal
        title={t("edit_product")}
        open={isEditModalOpen}
        onClose={handleCloseEditModal}
        actions={editModalActions}
        size="large"
      >
        <div className="edit-form">
          {/* Basic Information Section */}
          <div className="form-section">
            <h3>{t("Basic Information")}</h3>
            <div className="form-section-content">
              <div className="form-row">
                <TextInput
                  name="product_name"
                  label={t("product_name")}
                  value={editFormData.product_name}
                  onChange={(value) => handleInputChange("product_name", value)}
                  required
                />
                <TextInput
                  name="brand_name"
                  label={t("brand")}
                  value={editFormData.brand_name}
                  onChange={(value) => handleInputChange("brand_name", value)}
                />
              </div>

              <MarkdownEditor
                name="product_description"
                label={t("description")}
                value={editFormData.product_description || ""}
                onChange={(value) =>
                  handleInputChange("product_description", value)
                }
                subtitle={t(
                  "Enter a detailed description using Markdown or HTML"
                )}
              />
              <MarkdownEditor
                name="short_description"
                label={t("short_description")}
                value={editFormData.short_description || ""}
                onChange={(value) =>
                  handleInputChange("short_description", value)
                }
                subtitle={t("Enter a brief summary using Markdown or HTML")}
              />

              <div className="form-row">
                <TextInput
                  name="effects"
                  label={t("effects")}
                  value={
                    // Display effects: stringify if object, otherwise show string or empty
                    editFormData.effects
                      ? typeof editFormData.effects === "object"
                        ? JSON.stringify(editFormData.effects, null, 2)
                        : String(editFormData.effects)
                      : ""
                  }
                  onChange={(value) => handleInputChange("effects", value)}
                  subtitle={t(
                    'Enter as comma-separated list (e.g., \'Relaxed, Happy\') or JSON object (e.g., {"description": "Relaxing", "onset": "5-10 min"})'
                  )}
                  textarea // Use textarea for potentially larger JSON
                />
                <TextInput
                  name="mood"
                  label={t("mood")}
                  value={
                    // Display mood: join if array, otherwise show string or empty
                    Array.isArray(editFormData.mood)
                      ? editFormData.mood.join(", ")
                      : typeof editFormData.mood === "string"
                      ? editFormData.mood
                      : ""
                  }
                  onChange={(value) => handleInputChange("mood", value)}
                  subtitle={t("e.g., Sleepy, Energetic, Creative")}
                />
              </div>

              <div className="form-row">
                <CategorySelect
                  label={t("category")}
                  value={editFormData.category || ""}
                  onChange={(value) => handleInputChange("category", value)}
                />
                <TextInput
                  name="subcategory"
                  label={t("subcategory")}
                  value={editFormData.subcategory || ""}
                  onChange={(value) => handleInputChange("subcategory", value)}
                />
              </div>

              <TextInput
                name="product_tags"
                label={t("tags")}
                value={
                  // Display tags: join if array, otherwise show string or empty
                  Array.isArray(editFormData.product_tags)
                    ? editFormData.product_tags.join(", ")
                    : typeof editFormData.product_tags === "string"
                    ? editFormData.product_tags
                    : ""
                }
                onChange={(value) => handleInputChange("product_tags", value)}
                placeholder="tag1, tag2, tag3"
              />
            </div>
          </div>

          {/* Product Image Section */}
          <div className="form-section">
            <h3>{t("Product Image")}</h3>
            <div className="form-section-content">
              <ImageUploadWithDragDrop
                onImageChange={handleImageChange}
                onClear={clearImage}
                imagePreview={imagePreview}
                imageFile={(editFormData as any).new_image_file}
                label={t("Product Image")}
                placeholder={t("Drag and drop an image here, or click to select")}
              />

              {!imagePreview && !(editFormData as any).new_image_file && (
                <div className="image-url-input" style={{ marginTop: '16px' }}>
                  <TextInput
                    label={t("Or enter image URL")}
                    name="image_url"
                    value={editFormData.image_url || ""}
                    onChange={(value) =>
                      handleInputChange("image_url", value)
                    }
                    disabled={
                      (editFormData as any).new_image_file !== undefined
                    }
                    subtitle={
                      (editFormData as any).new_image_file
                        ? t("Clear uploaded image to use URL")
                        : ""
                    }
                  />
                </div>
              )}
            </div>
          </div>

          {/* Product Details Section */}
          <div className="form-section">
            <h3>{t("Product Details")}</h3>
            <div className="form-section-content">
              <div className="form-row">
                <TextInput
                  name="percentage_thc"
                  label={t("THC %")}
                  value={
                    editFormData.percentage_thc !== undefined &&
                    editFormData.percentage_thc !== null
                      ? editFormData.percentage_thc.toString()
                      : ""
                  }
                  type="number"
                  onChange={(value) =>
                    handleInputChange(
                      "percentage_thc",
                      value ? Number(value) : null
                    )
                  }
                />
                <TextInput
                  name="percentage_cbd"
                  label={t("CBD %")}
                  value={
                    editFormData.percentage_cbd !== undefined &&
                    editFormData.percentage_cbd !== null
                      ? editFormData.percentage_cbd.toString()
                      : ""
                  }
                  type="number"
                  onChange={(value) =>
                    handleInputChange(
                      "percentage_cbd",
                      value ? Number(value) : null
                    )
                  }
                />
                <TextInput
                  name="latest_price"
                  label={t("price")}
                  value={
                    editFormData.latest_price !== undefined &&
                    editFormData.latest_price !== null
                      ? editFormData.latest_price.toString()
                      : ""
                  }
                  type="number"
                  onChange={(value) =>
                    handleInputChange(
                      "latest_price",
                      value ? Number(value) : null
                    )
                  }
                />
              </div>

              <div className="form-row">
                <CheckboxInput
                  label={t("medical")}
                  checked={!!editFormData.medical}
                  onChange={(value) => handleInputChange("medical", value)}
                />
                <CheckboxInput
                  label={t("recreational")}
                  checked={!!editFormData.recreational}
                  onChange={(value) => handleInputChange("recreational", value)}
                />
              </div>
            </div>
          </div>

          {/* Required IDs Section */}
          <div className="form-section">
            <h3>{t("Required IDs")}</h3>
            <div className="form-section-content">
              <div className="form-row">
                <TextInput
                  name="meta_sku"
                  label={t("Meta SKU")}
                  value={editFormData.meta_sku || ""}
                  onChange={(value) => handleInputChange("meta_sku", value)}
                  disabled
                />
                <TextInput
                  name="retailer_id"
                  label={t("Retailer ID")}
                  value={editFormData.retailer_id || ""}
                  onChange={(value) => handleInputChange("retailer_id", value)}
                />
              </div>
            </div>
          </div>

          {/* Additional Details Section */}
          <div className="form-section expandable">
            <div
              className="expandable-header"
              onClick={toggleAdditionalDetailsEdit}
            >
              <h3>{t("Additional Details")}</h3>
              <span className="expandable-icon">
                {showAdditionalDetailsEdit ? (
                  <FiChevronUp />
                ) : (
                  <FiChevronDown />
                )}
              </span>
            </div>

            {showAdditionalDetailsEdit && (
              <div className="form-section-content">
                <div className="form-row">
                  <TextInput
                    label={t("Weight")}
                    name="display_weight"
                    value={editFormData.display_weight || ""}
                    onChange={(value) =>
                      handleInputChange("display_weight", value)
                    }
                  />
                  <TextInput
                    label={t("Quantity Per Package")}
                    name="quantity_per_package"
                    type="number"
                    value={
                      editFormData.quantity_per_package !== undefined &&
                      editFormData.quantity_per_package !== null
                        ? editFormData.quantity_per_package.toString()
                        : ""
                    }
                    onChange={(value) =>
                      handleInputChange(
                        "quantity_per_package",
                        value ? Number(value) : null
                      )
                    }
                  />
                </div>

                <div className="form-row">
                  <TextInput
                    label={t("THC (mg)")}
                    name="mg_thc"
                    type="number"
                    value={
                      editFormData.mg_thc !== undefined &&
                      editFormData.mg_thc !== null
                        ? editFormData.mg_thc.toString()
                        : ""
                    }
                    onChange={(value) =>
                      handleInputChange("mg_thc", value ? Number(value) : null)
                    }
                  />
                  <TextInput
                    label={t("CBD (mg)")}
                    name="mg_cbd"
                    type="number"
                    value={
                      editFormData.mg_cbd !== undefined &&
                      editFormData.mg_cbd !== null
                        ? editFormData.mg_cbd.toString()
                        : ""
                    }
                    onChange={(value) =>
                      handleInputChange("mg_cbd", value ? Number(value) : null)
                    }
                  />
                </div>

                <TextInput
                  label={t("Menu Provider")}
                  name="menu_provider"
                  value={editFormData.menu_provider || ""}
                  onChange={(value) =>
                    handleInputChange("menu_provider", value)
                  }
                />

                <TextInput
                  label={t("Product URL")}
                  name="url"
                  value={editFormData.url || ""}
                  onChange={(value) => handleInputChange("url", value)}
                />
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Delete Modal */}
      <Modal
        title={t("delete_product")}
        open={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        actions={deleteModalActions}
      >
        <p>
          {t("delete_product_confirmation", {
            name: product.product_name,
          })}
        </p>
      </Modal>

      {/* Enhance with AI Modal */}
      <Modal
        title="Enhance Product with AI"
        open={isEnhanceModalOpen}
        onClose={handleCloseEnhanceModal}
        actions={enhanceModalActions}
      >
        <div className="enhance-modal-content">
          <p className="mb-4">
            AI will analyze this product and enhance it by adding or improving:
          </p>
          <ul className="list-disc pl-5 mb-4">
            <li>Product description</li>
            <li>Categorization (category & subcategory)</li>
            <li>Product tags</li>
            <li>Effects and moods</li>
            <li>THC/CBD percentages (if missing)</li>
          </ul>

          <div className="bg-blue-100 p-3 rounded mb-4">
            <p className="text-blue-800">
              <strong>Note:</strong> Processing runs in the background and may
              take several minutes.
            </p>
          </div>

          {product.enhancement_status === "complete" && (
            <div className="border-t pt-4 mt-4">
              <p className="mb-2 font-semibold">
                Current AI Enhancement Status:
              </p>
              <div className="bg-green-100 p-3 rounded">
                <p className="text-green-800 flex items-center">
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  This product has already been enhanced by AI
                </p>
                {product.ai_enhanced_fields &&
                  product.ai_enhanced_fields.length > 0 && (
                    <p className="text-green-700 mt-1 text-sm">
                      Enhanced fields: {product.ai_enhanced_fields.join(", ")}
                    </p>
                  )}
              </div>
              <p className="mt-3 text-gray-600">
                You can re-enhance this product to improve its data further or
                update existing AI-generated content.
              </p>
            </div>
          )}

          {product.enhancement_status === "failed" && (
            <div className="border-t pt-4 mt-4">
              <p className="mb-2 font-semibold">
                Current AI Enhancement Status:
              </p>
              <div className="bg-red-100 p-3 rounded">
                <p className="text-red-800 flex items-center">
                  <span className="w-4 h-4 mr-2">⚠️</span>
                  Previous enhancement attempt failed
                </p>
                {product.enhancement_error && (
                  <p className="text-red-700 mt-1 text-sm">
                    Error: {product.enhancement_error}
                  </p>
                )}
              </div>
            </div>
          )}

          {isEnhancing && (
            <div className="mt-4 flex items-center justify-center">
              <div className="spinner mr-2"></div>
              <span>Queuing product enhancement job...</span>
            </div>
          )}
        </div>
      </Modal>
    </PageContent>
  );
}
